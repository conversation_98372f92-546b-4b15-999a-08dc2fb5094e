import React, { useEffect, useState, useCallback } from "react";
import {
  Box,
  Button,
  Card,
  Collapse,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  styled,
  TextField,
  Snackbar,
  Switch,
  FormControlLabel,
  Menu,
  Checkbox,
  ListItemText,
} from "@mui/material";
import {
  Close,
  FilterList,
  ExpandMore,
  ExpandLess,
} from "@mui/icons-material";
import httpclient from "../../../Utils";
import MuiAlert from "@mui/material/Alert";
import MatrixProductTable from "./MatrixProductTable";
import useTokenRefresh from "../../../Hooks/useTokenRefresh";
import Footer from "../../../Components/Footer";

const Alert = React.forwardRef(function Alert(props, ref) {
  return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});

const FilteredBox = styled(Box)(({ theme }) => ({
  background: "#f9f9f9",
  padding: "5px 10px",
  borderRadius: "5px",
  "& p": {
    margin: "0",
    marginRight: "10px",
    display: "inline-block",
    background: "#dedede",
    borderRadius: "10px",
    padding: "2px 5px",
  },
  "& svg": {
    fontSize: "15px",
    cursor: "pointer",
    position: "relative",
    top: "3px",
    background: theme.palette.primary.dark,
    color: "#fff",
    borderRadius: "50%",
    padding: "2px",
    marginLeft: "2px",
  },
}));

const Header = styled("div")(({ theme }) => ({
  "& h1": {
    color: theme.palette.primary.dark,
    margin: "0",
  },
}));

const ErplyProductsMappingDetails = () => {
  const [rows, setRows] = useState([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(20);
  const [total, setTotal] = useState(0);
  const [from, setFrom] = useState(0);
  const [to, setTo] = useState(0);
  const [currentColumn, setCurrentColumn] = useState("");
  const [direction, setDirection] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [showChildrenAlso, setShowChildrenAlso] = useState(false);
  const [expandedRows, setExpandedRows] = useState(new Set());
  const [expandAll, setExpandAll] = useState(false);
  const [validationTypes, setValidationTypes] = useState([]); // Array of active validation types
  const [validationMenuAnchor, setValidationMenuAnchor] = useState(null);

  // Filter states
  const [filterData, setFilterData] = useState({
    code: "",
    status: "",
    mappingStatus: "",
    validateMapping: "",
    matrixMappingValidation: false,
  });

  // Snackbar states
  const [open, setOpen] = useState(false);
  const [message, setMessage] = useState("");
  const [messageState, setMessageState] = useState("");

  const { checkTokenAndRefresh } = useTokenRefresh();

  // Matrix Products Columns
  const matrixColumns = [
    ...(showChildrenAlso ? [{ id: "expand", name: "" }] : []),
    { id: "type", name: "Type" },
    { id: "productID", name: "Product ID" },
    { id: "name", name: "Product Name" },
    { id: "code", name: "Code" },
    { id: "priceWithVat", name: "Price with VAT" },
    { id: "status", name: "Status" },
    { id: "shopifyProductId", name: "Shopify Product ID" },
    { id: "shopifyTitle", name: "Shopify Title" },
    { id: "shopifyHandle", name: "Shopify Handle" },
    { id: "shopifyStatus", name: "Shopify Status" },
  ];

  // Product Variants Columns
  const variantColumns = [
    { id: "expand", name: "" }, // Empty expand column for alignment
    { id: "type", name: "Type" },
    { id: "erplySku", name: "SKU" },
    { id: "erplyBarcode", name: "Barcode" },
    { id: "erplyColorSize", name: "Color/ Size" },
    { id: "priceWithVat", name: "Price with VAT" },
    { id: "status", name: "Status" },

    { id: "shopifySku", name: "SKU" },
    { id: "shopifyBarcode", name: "Barcode" },
    { id: "shopifyColorSize", name: "Color/ Size" },
    { id: "shopifyStatus", name: "Shopify Status" },
  ];

  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      // await checkTokenAndRefresh();

      let apiUrl = `/request-response?requestName=erply/v2/products&serviceType=shopify&showVariations=1&pagination=${rowsPerPage}&page=${page}`;

      if (currentColumn) {
        apiUrl += `&sort=${currentColumn}&direction=${direction ? 'asc' : 'desc'}`;
      }

      const response = await httpclient.get(apiUrl);

      if (response.data && response.data.data) {
        setRows(response.data.data);
        setTotal(response.data?.meta.total || 0);
        setFrom(response.data?.meta.from || 0);
        setTo(response.data?.meta.to || 0);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
      setMessage("Error fetching data");
      setMessageState("error");
      setOpen(true);
    } finally {
      setLoading(false);
    }
  }, [rowsPerPage, page, currentColumn, direction]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const handleSort = (columnId) => {
    if (currentColumn === columnId) {
      setDirection(!direction);
    } else {
      setCurrentColumn(columnId);
      setDirection(false);
    }
  };

  const handleChangePage = (_, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(1);
  };

  const handleFilterChange = (field, value) => {
    setFilterData(prev => ({
      ...prev,
      [field]: value
    }));
    fetchFilteredData()
  };

  const applyFilters = () => {
    setPage(1);
    fetchFilteredData();
  };

  const fetchFilteredData = async () => {
    setLoading(true);

    try {
      // Base URL and parameters
      const params = new URLSearchParams({
        requestName: 'erply/v2/products',
        serviceType: 'shopify',
        pagination: rowsPerPage.toString(),
        page: page.toString(),
      });

      let baseUrl = '/request-response';

      // Matrix mapping override
      if (filterData.matrixMappingValidation === true) {
        baseUrl = '/request-response';
        params.set('requestName', 'erply/v2/products');
        params.set('serviceType', 'shopify');
        params.set('isMappingStatus', '1');
      }

      // Add filter-based params
      for (const [key, value] of Object.entries(filterData)) {
        if (!value) continue;

        switch (key) {
          case 'mappingStatus':
            const statusKey = value === 'Mapped' ? '$notNull' : '$null';
            params.append(`filters[shopifyProductID][${statusKey}]`, value);
            break;

          case 'validateMapping':
            params.set('isMappingStatus', '1');
            params.set('filterType', value);
            break;

          case 'matrixMappingValidation':
            // Already handled above
            break;

          default:
            params.append(`filters[${key}][$eq]`, value);
        }
      }

      // Sorting
      if (currentColumn) {
        params.append('sort', currentColumn);
        params.append('direction', direction ? 'asc' : 'desc');
      }

      // Final API URL
      const apiUrl = `${baseUrl}?${params.toString()}`;

      // Fetch data
      const response = await httpclient.get(apiUrl);

      if (response.data?.data) {
        setRows(response.data.data);
        setTotal(response.data?.meta?.total || 0);
        setFrom(response.data?.meta?.from || 0);
        setTo(response.data?.meta?.to || 0);
      }
    } catch (error) {
      console.error("Error fetching filtered data:", error);
      setMessage("Error fetching filtered data");
      setMessageState("error");
      setOpen(true);
    } finally {
      setLoading(false);
    }
  };


  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Header>
          <h1>Matrix Products Mapping Details</h1>
        </Header>
      </Grid>

      {/* Filter Section */}
      <Grid item xs={12}>
        <Card sx={{ padding: "20px", marginBottom: "20px" }}>
          <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
            <Button
              variant="outlined"
              startIcon={<FilterList />}
              onClick={() => setShowFilters(!showFilters)}
            >
              {showFilters ? "Hide Filters" : "Show Filters"}
            </Button>

            <Box display="flex" alignItems="center" gap={2}>
              <FormControlLabel
                control={
                  <Switch
                    checked={showChildrenAlso}
                    onChange={(e) => setShowChildrenAlso(e.target.checked)}
                  />
                }
                label="Show Variants Also"
              />

              <Button
                variant="outlined"
                startIcon={expandAll ? <ExpandLess /> : <ExpandMore />}
                onClick={() => {
                  if (showChildrenAlso) {
                    // When "Show Children Also" is enabled, toggle expand all
                    if (expandAll) {
                      setExpandedRows(new Set());
                    } else {
                      const allMatrixIds = rows
                        .filter(row => row.type === 'MATRIX')
                        .map(row => row.productID);
                      setExpandedRows(new Set(allMatrixIds));
                    }
                    setExpandAll(!expandAll);
                  } else {
                    // When "Show Children Also" is disabled, just enable it and expand all
                    setShowChildrenAlso(true);
                    const allMatrixIds = rows
                      .filter(row => row.type === 'MATRIX')
                      .map(row => row.productID);
                    setExpandedRows(new Set(allMatrixIds));
                    setExpandAll(true);
                  }
                }}
                disabled={!showChildrenAlso && rows.filter(row => row.type === 'MATRIX').length === 0}
              >
                {showChildrenAlso ? (expandAll ? "Collapse All" : "Expand All") : "Show All Variants"}
              </Button>

              <Button
                variant={validationTypes.length > 0 ? "contained" : "outlined"}
                color={validationTypes.length > 0 ? "warning" : "primary"}
                onClick={(e) => setValidationMenuAnchor(e.currentTarget)}
                disabled={!showChildrenAlso}
              >
                {validationTypes.length > 0
                  ? `Validating (${validationTypes.length})`
                  : "Validate Mapping"
                }
              </Button>

              <Menu
                anchorEl={validationMenuAnchor}
                open={Boolean(validationMenuAnchor)}
                onClose={() => setValidationMenuAnchor(null)}
              >
                <MenuItem
                  onClick={() => {
                    const newTypes = validationTypes.includes('sku')
                      ? validationTypes.filter(type => type !== 'sku')
                      : [...validationTypes, 'sku'];
                    setValidationTypes(newTypes);
                  }}
                >
                  <Checkbox checked={validationTypes.includes('sku')} />
                  <ListItemText primary="SKU Validation" />
                </MenuItem>

                <MenuItem
                  onClick={() => {
                    const newTypes = validationTypes.includes('barcode')
                      ? validationTypes.filter(type => type !== 'barcode')
                      : [...validationTypes, 'barcode'];
                    setValidationTypes(newTypes);
                  }}
                >
                  <Checkbox checked={validationTypes.includes('barcode')} />
                  <ListItemText primary="Barcode Validation" />
                </MenuItem>

                <MenuItem
                  onClick={() => {
                    const newTypes = validationTypes.includes('price')
                      ? validationTypes.filter(type => type !== 'price')
                      : [...validationTypes, 'price'];
                    setValidationTypes(newTypes);
                  }}
                >
                  <Checkbox checked={validationTypes.includes('price')} />
                  <ListItemText primary="Price Validation" />
                </MenuItem>
              </Menu>
            </Box>
          </Box>

          <Collapse in={showFilters}>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="Code"
                  value={filterData.code}
                  onChange={(e) => handleFilterChange("code", e.target.value)}
                  size="small"
                />
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={filterData.status}
                    label="Status"
                    onChange={(e) => handleFilterChange("status", e.target.value)}
                  >
                    <MenuItem value="">All</MenuItem>
                    <MenuItem value="ACTIVE">Active</MenuItem>
                    <MenuItem value="INACTIVE">Inactive</MenuItem>
                    <MenuItem value="ARCHIVED">Archived</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>Mapping Status</InputLabel>
                  <Select
                    value={filterData.mappingStatus}
                    label="Mapping Status"
                    onChange={(e) => handleFilterChange("mappingStatus", e.target.value)}
                  >
                    <MenuItem value="">All</MenuItem>
                    <MenuItem value="Mapped">Mapped</MenuItem>
                    <MenuItem value="Unmapped">Unmapped</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>Validate Mapping</InputLabel>
                  <Select
                    value={filterData.mappingStatus}
                    label="Mapping Validation"
                    onChange={(e) => handleFilterChange("validateMapping", e.target.value)}
                  >
                    <MenuItem value="">All</MenuItem>
                    <MenuItem value="price">Price</MenuItem>
                    <MenuItem value="barcode">Barcode</MenuItem>
                    <MenuItem value="sku">SKU</MenuItem>
                    <MenuItem value="colorsize">Color/Size</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={filterData.matrixMappingValidation || false}
                      onChange={(e) => handleFilterChange("matrixMappingValidation", e.target.checked)}
                    />
                  }
                  label="Matrix Mapping Validation"
                />
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Box display="flex" gap={1}>
                  <Button
                    variant="contained"
                    onClick={applyFilters}
                    fullWidth
                  >
                    Apply Filters
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={() => {
                      setFilterData({
                        code: "",
                        productID: "",
                        type: "",
                        status: "",
                        categoryName: "",
                        groupName: "",
                        mappingStatus: "",
                        validateMapping: "",
                        matrixMappingValidation: false,
                      });
                      setPage(1);
                      fetchData();
                    }}
                    fullWidth
                  >
                    Clear
                  </Button>
                </Box>
              </Grid>
            </Grid>

            {/* Active Filters Display */}
            {Object.values(filterData).some(value => value !== "") && (
              <FilteredBox sx={{ mt: 2 }}>
                <strong>Active Filters: </strong>
                {Object.entries(filterData).map(([key, value]) =>
                  value ? (
                    <p key={key}>
                      {key}: {value}
                      <Close
                        onClick={() => {
                          handleFilterChange(key, "");
                          setTimeout(applyFilters, 100);
                        }}
                      />
                    </p>
                  ) : null
                )}
              </FilteredBox>
            )}
          </Collapse>
        </Card>
      </Grid>

      <Grid item xs={12}>
        <MatrixProductTable
          matrixColumns={matrixColumns}
          variantColumns={variantColumns}
          rows={rows}
          loading={loading}
          currentColumn={currentColumn}
          direction={direction}
          page={page}
          total={total}
          fromTable={from}
          toTable={to}
          rowsPerPage={rowsPerPage}
          onSort={handleSort}
          onChangePage={handleChangePage}
          onChangeRowsPerPage={handleChangeRowsPerPage}
          expandedRows={expandedRows}
          setExpandedRows={setExpandedRows}
          showChildrenAlso={showChildrenAlso}
          validationTypes={validationTypes}
        />
      </Grid>

      <Footer />

      <Snackbar
        open={open}
        autoHideDuration={6000}
        onClose={() => setOpen(false)}
      >
        <Alert onClose={() => setOpen(false)} severity={messageState}>
          {message}
        </Alert>
      </Snackbar>
    </Grid>
  );
};

export default ErplyProductsMappingDetails;